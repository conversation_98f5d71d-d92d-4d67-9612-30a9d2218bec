<script setup lang="ts">
import { ref } from 'vue';

const isMaximized = ref(false);
const handleMinimize = () => {
  window.ipcRenderer.send('window-minimize');
};

const handleMaximize = () => {
  window.ipcRenderer.send('window-maximize');
};

const handleClose = () => {
  window.ipcRenderer.send('window-close');
};

// 监听窗口状态变化
window.ipcRenderer.on('window-state-changed', (event, max: boolean) => {
  console.log(111);

  isMaximized.value = max;
});
</script>

<template>
  <div class="app-header" z-1000 flex aic jcsb h-30 bg-dark>
    <div class="title" pl-20 c-white fs-16>爱建策略交易系统</div>
    <div class="controls" h-full z-1000 flex aic>
      <div
        flex
        aic
        jcc
        w-30
        h-full
        hover="bg-[--g-bg-hover2]"
        c-white
        cursor-pointer
        @click="handleMinimize"
      >
        <i i-mdi-window-minimize block></i>
      </div>
      <div
        flex
        aic
        jcc
        w-30
        h-full
        hover="bg-[--g-bg-hover2]"
        c-white
        cursor-pointer
        @click="handleMaximize"
      >
        <i :class="isMaximized ? 'i-mdi-window-restore' : 'i-mdi-window-maximize'"></i>
      </div>
      <div
        flex
        aic
        jcc
        w-30
        h-full
        hover="bg-[--g-bg-hover2]"
        c-white
        cursor-pointer
        @click="handleClose"
      >
        <i i-mdi-window-close></i>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-header {
  -webkit-app-region: drag;
  .controls {
    -webkit-app-region: no-drag;
  }
}
</style>
