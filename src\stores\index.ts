import { ref } from 'vue';
import { defineStore } from 'pinia';
import type { Pool, PoolDetail, InstrumentInfo } from '@/types';
import { MarketService } from '@/api';
import { AssetTypeEnum } from '@/enum';

// 股票选择状态管理
export const useStockSelectionStore = defineStore('stockSelection', () => {
  const selectedStock = ref<PoolDetail | null>(null);

  const setSelectedStock = (stock: PoolDetail | null) => {
    selectedStock.value = stock;
  };

  return {
    selectedStock,
    setSelectedStock,
  };
});

// 股票池选择状态管理
export const usePoolSelectionStore = defineStore('poolSelection', () => {
  const selectedPool = ref<Pool | null>(null);

  const setSelectedPool = (pool: Pool | null) => {
    selectedPool.value = pool;
  };

  return {
    selectedPool,
    setSelectedPool,
  };
});

// 股票数据缓存管理
export const useInstrumentStore = defineStore('instrument', () => {
  // 缓存不同资产类型的股票数据
  const instrumentsCache = new Map<number, InstrumentInfo[]>();
  // 记录正在加载的资产类型，避免重复请求
  const loadingAssetTypes = new Set<number>();

  /**
   * 获取指定资产类型的股票列表
   * @param assetType 资产类型
   * @returns 股票列表
   */
  const getInstruments = async (assetType: number): Promise<InstrumentInfo[]> => {
    // 如果已有缓存，直接返回
    if (instrumentsCache.has(assetType)) {
      return instrumentsCache.get(assetType)!;
    }

    // 如果正在加载，等待加载完成
    if (loadingAssetTypes.has(assetType)) {
      // 轮询等待加载完成
      while (loadingAssetTypes.has(assetType)) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return instrumentsCache.get(assetType) || [];
    }

    try {
      // 标记为正在加载
      loadingAssetTypes.add(assetType);

      const { errorCode, data } = await MarketService.downloadInstruments(assetType);
      if (errorCode === 0 && data) {
        // 缓存数据
        instrumentsCache.set(assetType, data);
        return data;
      }
      return [];
    } catch (error) {
      console.error('获取股票列表失败:', error);
      return [];
    } finally {
      // 移除加载标记
      loadingAssetTypes.delete(assetType);
    }
  };

  /**
   * 获取股票列表（专门用于股票类型）
   */
  const getStockInstruments = () => getInstruments(AssetTypeEnum.股票);

  /**
   * 清除指定资产类型的缓存
   * @param assetType 资产类型，不传则清除所有缓存
   */
  const clearCache = (assetType?: number) => {
    if (assetType !== undefined) {
      instrumentsCache.delete(assetType);
    } else {
      instrumentsCache.clear();
    }
  };

  /**
   * 检查是否已缓存指定资产类型的数据
   * @param assetType 资产类型
   */
  const hasCache = (assetType: number) => instrumentsCache.has(assetType);

  return {
    getInstruments,
    getStockInstruments,
    clearCache,
    hasCache,
  };
});
