<script setup lang="ts">
import { shallowRef } from 'vue';
import SellPositionTable from './AutoSell/SellPositionTable.vue';
import SellSummaryTable from './AutoSell/SellSummaryTable.vue';
import SellOrderTable from './AutoSell/SellOrderTable.vue';
import SellStrategySetting from './AutoSell/SellStrategySetting.vue';
import type { TablePositionInfo } from '@/types';

// 当前选中的持仓
const selectedPosition = shallowRef<TablePositionInfo | null>(null);

// 处理持仓选择
const handlePositionSelect = (position: TablePositionInfo) => {
  selectedPosition.value = position;
};

// 处理策略保存成功，刷新持仓表格
const positionTableRef = shallowRef<InstanceType<typeof SellPositionTable>>();
const handleStrategySaved = () => {
  positionTableRef.value?.refreshData();
};
</script>

<template>
  <div overflow-hidden flex p-10 gap-10>
    <div flex="~ col 1" min-w-1 gap-4>
      <SellPositionTable
        overflow-hidden
        flex-1
        bg="[--g-panel-bg]"
        ref="positionTableRef"
        @position-select="handlePositionSelect"
      />
      <SellSummaryTable bg="[--g-panel-bg]" overflow-hidden flex-1 />
      <SellOrderTable bg="[--g-panel-bg]" overflow-hidden flex-1 />
    </div>
    <SellStrategySetting
      w="50%"
      min-w-860
      bg="[--g-panel-bg]"
      :selected-position="selectedPosition"
      @strategy-saved="handleStrategySaved"
    />
  </div>
</template>

<style scoped></style>
