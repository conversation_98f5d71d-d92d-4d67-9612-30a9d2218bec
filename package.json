{"name": "vite-project", "version": "0.0.0", "private": true, "main": "dist-electron/main.js", "scripts": {"dev:web": "vite --config vite/web.config.ts", "build:web": "run-p type-check && vite --config vite/web.config.ts build", "preview": "vite --config vite/web.config.ts preview", "dev:electron": "vite --config vite/electron.config.ts", "build:electron": "run-p type-check && vite --config vite/electron.config.ts build && electron-builder", "test": "vitest --config vite/vitest.config.ts", "prepare": "husky install", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "qs": "^6.14.0", "vue": "^3.5.17", "vue-router": "^4.5.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^24.0.10", "@types/qs": "^6.14.0", "@unocss/preset-rem-to-px": "^66.3.2", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "electron": "^37.1.0", "electron-builder": "^26.0.12", "eslint": "^9.30.1", "eslint-plugin-oxlint": "^1.5.0", "eslint-plugin-vue": "~10.2.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "npm-run-all2": "^8.0.4", "oxlint": "^1.5.0", "prettier": "3.6.2", "typescript": "~5.8.3", "unocss": "^66.3.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-mock-dev-server": "^1.9.1", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^3.0.0"}, "lint-staged": {"*.{ts,vue}": ["npm run lint:eslint", "npm run format"]}}