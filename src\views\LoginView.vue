<script setup lang="ts">
import { ref, shallowRef, useTemplateRef } from 'vue';
import { ElMessage, ElForm } from 'element-plus';
import { LoginService } from '@/api';
import { ws } from '@/api/websocket';
import { Misc } from '@/script';
import router from '@/router';
import type { ServerInfo } from '@/types';

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  server: [{ required: true, message: '请选择服务器', trigger: 'change' }],
};

const loading = ref(false);

const form = ref({
  username: 'zt_aijian1',
  password: '111111',
  server: '',
});

const formRef = useTemplateRef('formRef');

const servers = shallowRef<ServerInfo[]>([]);

const initServers = async () => {
  // 加载服务器列表
  const res = await fetch('/servers.json');
  const data = await res.json();
  servers.value = data;
  // localStorage中有缓存的服务器信息并且在服务器列表中存在，则回显缓存的服务器，否则清除缓存服务器信息并选择第一个服务器
  const cacheServer = Misc.getServer();
  if (cacheServer && servers.value.find(server => server.name === cacheServer.name)) {
    form.value.server = cacheServer.name;
  } else {
    form.value.server = servers.value[0].name;
    Misc.setServer(servers.value[0]);
  }
};

initServers();

const handleServerChange = (value: string) => {
  Misc.setServer(servers.value.find(server => server.name === value)!);
};

const login = () => {
  if (!formRef.value) return;
  formRef.value.validate(valid => {
    if (valid) {
      try {
        loading.value = true;
        LoginService.loginByWS(form.value.username, form.value.password)
          .then(data => {
            if (data.errorCode !== 0) {
              ElMessage.error(data.errorMsg || '登录失败');
              return;
            }
            Misc.setUser({ ...data, lname: form.value.username, lpass: form.value.password });
            // 登录成功后启动token自动刷新
            ws.startTokenAutoRefresh();
            router.push({ name: 'main' });
          })
          .catch((error: Error) => {
            ElMessage.error(error.message);
          });
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<template>
  <div class="login-view" m30 flex justify-center aic>
    <div class="login-box" p-20 b-rd-8 w-300 flex flex-col aic jcc bg="[--g-panel-bg]">
      <el-form w-full ref="formRef" :model="form" :rules="rules" label-width="60px">
        <el-form-item label="用户名" prop="username">
          <el-input placeholder="请输入用户名" v-model="form.username" clearable />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" clearable />
        </el-form-item>
        <el-form-item label="服务器" prop="server">
          <el-select placeholder="请选择服务器" v-model="form.server" @change="handleServerChange">
            <el-option
              v-for="server in servers"
              :key="server.name"
              :value="server.name"
              :label="server.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="login-button" type="primary" :disabled="loading" @click="login">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped></style>
