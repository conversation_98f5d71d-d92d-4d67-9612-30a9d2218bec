import type { FunctionCodeBody, TokenResponse, WsMessage, ReqId, HandlersMap } from '@/types';
import { createLogger } from '../libs/logger';
import Misc from '../script/misc';
import { SendFunctionCode, ReceiveFunctionCode, ConnectionStatus } from '../enum';

const logger = createLogger('WebSocket');

/**
 * WebSocket 服务类
 * 实现 WebSocket 连接管理、消息收发、心跳检测和自动重连功能
 */
export class WebSocketService {
  private static instance: WebSocketService; // 单例实例
  private socket: WebSocket | null = null; // WebSocket 实例
  private reconnectAttempts = 0; // 当前重连尝试次数
  private readonly maxReconnectAttempts = 5; // 最大重连尝试次数
  private readonly reconnectDelayBase = 1000; // 重连基础延迟(毫秒)
  private heartbeatInterval?: number; // 心跳定时器ID
  private readonly heartbeatTime = 30000; // 心跳间隔时间(毫秒)
  private readonly headerLen = 20; // 消息头长度(字节)
  private isManualDisconnect = false; // 是否手动断开连接标志
  private messageHandlers: HandlersMap<any> = new Map(); // 消息处理器映射
  private status: ConnectionStatus = ConnectionStatus.断连; // 当前连接状态
  private usedReqIds = new Set<ReqId>(); // 已使用的reqId集合
  private readonly maxReqId = 65535; // reqId最大值
  private currentReqId = 0; // 当前reqId计数器
  private tokenRefreshInterval?: number; // token刷新定时器ID
  private readonly tokenRefreshTime = 30 * 60 * 1000; // token刷新间隔时间(30分钟)

  private constructor() {}

  // 暴露状态给日志记录器
  getStatus(): ConnectionStatus {
    return this.status;
  }

  getReconnectAttempts(): number {
    return this.reconnectAttempts;
  }

  /**
   * 获取 WebSocketService 单例实例
   * @returns WebSocketService 实例
   */
  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * 连接到 WebSocket 服务器
   * @param url WebSocket 服务器地址
   * @param callback 连接成功后的回调函数
   * @param reconnect 是否为重连
   */
  connect(url: string, callback?: () => void, reconnect = false): void {
    if (this.socket) {
      this.disconnect(reconnect);
    }

    logger.info('正在连接到服务器...', { url });
    this.socket = new WebSocket(url);

    this.status = ConnectionStatus.连接中;
    this.socket.onopen = () => {
      this.status = ConnectionStatus.已连接;
      this.reconnectAttempts = 0;
      this.isManualDisconnect = false;
      this.startHeartbeat();
      callback?.();
      logger.info('连接已建立', { url });
    };

    this.socket.onmessage = async event => {
      const msg = await this.decode(event.data);
      this.handleMessage(msg);
    };

    this.socket.onclose = event => {
      logger.info('连接已断开', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
      });

      this.status =
        this.reconnectAttempts < this.maxReconnectAttempts && !this.isManualDisconnect
          ? ConnectionStatus.重连中
          : ConnectionStatus.断连;
      this.stopHeartbeat();
      this.stopTokenRefresh();
      if (!this.isManualDisconnect) {
        this.attemptReconnect(url);
      }
      this.isManualDisconnect = false;
    };

    this.socket.onerror = error => {
      logger.error('连接错误:', {
        error,
        status: this.status,
        url,
      });
    };
  }

  /**
   * 注册消息处理器
   * @param reqId 请求ID
   * @param handler 处理函数
   */
  private registerHandler<T extends SendFunctionCode>(
    reqId: ReqId,
    handler: (data: FunctionCodeBody[T]) => void,
  ): void {
    this.messageHandlers.set(reqId, handler);
    logger.debug('注册消息处理器', { reqId });
  }

  /**
   * 生成新的reqId
   * @returns 新的reqId，范围在0-65535之间
   */
  private generateReqId(): ReqId {
    // 如果所有reqId都被使用，抛出错误
    if (this.usedReqIds.size >= this.maxReqId + 1) {
      throw new Error('所有reqId都已被使用，无法生成新的reqId');
    }

    // 从当前位置开始查找可用的reqId
    let reqId = this.currentReqId;
    while (this.usedReqIds.has(reqId)) {
      reqId = (reqId + 1) % (this.maxReqId + 1);
      // 如果回到起始位置，说明没有可用的reqId（理论上不会发生，因为上面已经检查过）
      if (reqId === this.currentReqId) {
        throw new Error('无法找到可用的reqId');
      }
    }

    this.usedReqIds.add(reqId);
    this.currentReqId = (reqId + 1) % (this.maxReqId + 1);
    logger.debug('生成新的reqId', { reqId, usedCount: this.usedReqIds.size });
    return reqId;
  }

  /**
   * 移除消息处理器
   * @param reqId 请求ID
   */
  private unregisterHandler(reqId: ReqId): void {
    this.messageHandlers.delete(reqId);
    this.usedReqIds.delete(reqId);
    logger.debug('移除消息处理器', { reqId, remainingCount: this.usedReqIds.size });
  }

  /**
   * 处理收到的 WebSocket 消息
   * @param message 收到的消息对象
   */
  private handleMessage<T extends ReceiveFunctionCode>(message: WsMessage<T>): void {
    const { fc, reqId, dataType, body } = message;
    logger.debug('接收消息:', {
      fc,
      reqId,
      dataType,
      body,
    });

    // 处理推送消息（不需要reqId匹配）
    if (this.isPushMessage(fc)) {
      this.handlePushMessage(fc, body!);
      return;
    }

    const handler = this.messageHandlers.get(reqId);
    if (handler) {
      try {
        handler(body);
      } catch (error) {
        logger.error('消息处理器执行错误:', {
          error,
          fc,
          reqId,
        });
      }
    } else {
      logger.warn('未注册消息处理器', { fc });
    }
  }

  /**
   * 判断是否为推送消息
   * @param fc 消息类型
   * @returns 是否为推送消息
   */
  private isPushMessage(fc: ReceiveFunctionCode): boolean {
    return fc >= 70001 && fc <= 70005;
  }

  /**
   * 处理推送消息
   * @param fc 消息类型
   * @param body 消息体
   */
  private handlePushMessage<T extends ReceiveFunctionCode>(fc: T, body: FunctionCodeBody[T]): void {
    logger.info('收到推送消息:', { fc, body });

    // 触发全局事件，让组件监听并处理
    const event = new CustomEvent('websocket-push', {
      detail: { fc, body },
    });
    window.dispatchEvent(event);
  }

  /**
   * 启动心跳检测
   * 每隔固定时间发送心跳消息以保持连接
   */
  private startHeartbeat(): void {
    logger.debug('启动心跳检测');
    this.heartbeatInterval = window.setInterval(() => {
      if (Misc.loggedIn()) {
        try {
          const heartbeatReqId = this.generateReqId();
          this.send(SendFunctionCode.心跳, heartbeatReqId, '', 0);
          // 心跳不需要等待响应，立即释放reqId
          this.usedReqIds.delete(heartbeatReqId);
        } catch (err) {
          logger.error('心跳发送失败:', { error: err });
        }
      } else {
        this.stopHeartbeat();
      }
    }, this.heartbeatTime);
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      logger.debug('停止心跳检测');
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
  }

  /**
   * 启动token刷新
   * 每隔30分钟请求一次新的token
   */
  private startTokenRefresh(): void {
    logger.debug('启动token刷新');
    this.stopTokenRefresh();
    this.tokenRefreshInterval = window.setInterval(() => {
      const user = Misc.getUser();
      if (user) {
        this.refreshToken().catch(err => {
          logger.error('token刷新失败:', { error: err });
        });
      } else {
        this.stopTokenRefresh();
      }
    }, this.tokenRefreshTime);
  }

  /**
   * 停止token刷新
   */
  private stopTokenRefresh(): void {
    if (this.tokenRefreshInterval) {
      logger.debug('停止token刷新');
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = undefined;
    }
  }

  /**
   * 更新本地存储的用户token
   */
  private updateStoredUserToken(newToken: string): void {
    const user = Misc.getUser();
    if (user) {
      user.token = newToken;
      Misc.setUser(user);
      logger.info('已更新本地用户token');
    }
  }

  /**
   * 刷新token
   */
  private async refreshToken(): Promise<void> {
    try {
      logger.debug('开始刷新token');
      const response = (await this.register(
        SendFunctionCode.请求TOKEN,
        undefined,
        6,
      )) as TokenResponse;
      if (response && typeof response === 'object' && 'token' in response) {
        this.updateStoredUserToken(response.token);
        logger.info('token刷新成功');
      } else {
        logger.error('token刷新响应格式错误:', { response });
      }
    } catch (err) {
      logger.error('token刷新请求失败:', { error: err });
      throw err;
    }
  }

  /**
   * 尝试重新连接
   * 使用指数退避算法计算重连延迟
   * @param url WebSocket 服务器地址
   */
  private attemptReconnect(url: string): void {
    const attempt = this.reconnectAttempts + 1;
    const delay = this.reconnectDelayBase * Math.pow(2, this.reconnectAttempts);

    logger.info('准备重连', {
      attempt,
      maxAttempts: this.maxReconnectAttempts,
      delay,
      url,
    });

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect(url, undefined, true);
      }, delay);
    } else {
      logger.warn('达到最大重连次数', {
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
      });
    }
  }

  /**
   * 发送 WebSocket 消息
   * @param fc 消息类型
   * @param reqId 请求ID
   * @param body 消息体
   * @param dataType 数据类型，默认为1
   * @throws 当连接未建立时抛出错误
   */
  private send(fc: SendFunctionCode, reqId: number, body?: any, dataType = 1): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      const message = { fc, reqId, dataType, body };

      logger.debug('发送消息:', message);

      try {
        this.socket.send(this.encode(message));
      } catch (error) {
        logger.error('消息发送失败:', message);
        throw error;
      }
    } else {
      const error = new Error('WebSocket未连接，无法发送消息');
      logger.error(error.message, {
        readyState: this.socket?.readyState,
        status: this.status,
      });
      throw error;
    }
  }

  /**
   * 编码消息为 ArrayBuffer
   * 消息格式: [fc(4B)][保留(4B)][reqId(4B)][dataType(4B)][bodyLen(4B)][body(nB)]
   * @param message 要编码的消息对象
   * @returns 编码后的 ArrayBuffer
   */
  private encode(message: WsMessage<any>): ArrayBuffer {
    try {
      if (message.body === null || message.body === undefined) {
        message.body = '';
      }
      if (typeof message.body !== 'string' && !(message.body instanceof ArrayBuffer)) {
        message.body = JSON.stringify(message.body);
      }

      const { fc, reqId, dataType, body } = message;
      const encoder = new TextEncoder();
      const bodyBytes = typeof body === 'string' ? encoder.encode(body).byteLength : 0;

      const buffer = new ArrayBuffer(this.headerLen + bodyBytes);
      const view = new DataView(buffer);

      // 写入大端序32位整数
      view.setInt32(0, fc, false);
      view.setInt32(4, 0, false);
      view.setInt32(8, reqId & 0xffff, false);
      view.setInt32(12, dataType, false);
      view.setInt32(16, bodyBytes, false);

      if (typeof body === 'string') {
        const bodyView = new Uint8Array(buffer, this.headerLen);
        encoder.encodeInto(body, bodyView);
      } else if (body instanceof ArrayBuffer) {
        const bodyView = new Uint8Array(buffer, this.headerLen);
        bodyView.set(new Uint8Array(body));
      }

      return buffer;
    } catch (error) {
      logger.error('消息编码失败:', {
        error,
        message,
      });
      throw error;
    }
  }

  /**
   * 解码 Blob 数据为消息对象
   * @param data 收到的 Blob 数据
   * @returns 解析后的消息对象 Promise
   */
  private decode(data: Blob): Promise<WsMessage<ReceiveFunctionCode>> {
    return new Promise((resolve, reject) => {
      data
        .arrayBuffer()
        .then(buffer => {
          try {
            const view = new DataView(buffer);
            const fc: ReceiveFunctionCode = view.getInt32(0, false);
            const reqId = view.getInt32(8, false);
            const dataType = view.getInt32(12, false);
            const bodyLength = view.getInt32(16, false);

            let body: any = '';
            if (bodyLength > 0) {
              const bodyBuffer = buffer.slice(this.headerLen, this.headerLen + bodyLength);
              try {
                const decoder = new TextDecoder();
                body = decoder.decode(bodyBuffer);
                body = JSON.parse(body);
              } catch (parseError) {
                logger.warn('消息体解析失败，使用原始数据:', {
                  error: parseError,
                  fc,
                  reqId,
                });
                body = bodyBuffer;
              }
            }

            resolve({
              fc,
              reqId,
              dataType,
              body,
            } as WsMessage<ReceiveFunctionCode>);
          } catch (error) {
            logger.error('消息解码失败:', {
              error,
              dataSize: buffer.byteLength,
            });
            reject(error);
          }
        })
        .catch(error => {
          logger.error('消息buffer获取失败:', {
            error,
            blobSize: data.size,
          });
          reject(error);
        });
    });
  }

  /**
   * 断开 WebSocket 连接
   * 设置手动断开标志以避免自动重连
   * @param reconnect 是否为重连
   */
  private disconnect(reconnect = false): void {
    logger.info('断开连接', {
      status: this.status,
      readyState: this.socket?.readyState,
      usedReqIds: this.usedReqIds.size,
    });

    if (this.socket) {
      this.isManualDisconnect = !reconnect;
      this.status = ConnectionStatus.断连;
      this.stopHeartbeat();
      this.stopTokenRefresh();
      // 清理所有消息处理器和reqId
      this.messageHandlers.clear();
      this.usedReqIds.clear();
      this.currentReqId = 0;
      this.socket.close();
      this.socket = null;
      logger.debug('已清理所有消息处理器和reqId');
    }
  }

  /**
   * 启动token自动刷新（用于用户登录成功后调用）
   */
  startTokenAutoRefresh(): void {
    if (this.status === ConnectionStatus.已连接 && Misc.loggedIn()) {
      this.startTokenRefresh();
      logger.info('已启动token自动刷新');
    } else {
      logger.warn('无法启动token自动刷新:', {
        status: this.status,
        hasUser: Misc.loggedIn(),
      });
    }
  }

  /**
   * 停止token自动刷新（用于用户登出时调用）
   */
  stopTokenAutoRefresh(): void {
    this.stopTokenRefresh();
    logger.info('已停止token自动刷新');
  }

  register<T extends SendFunctionCode>(fc: T, body?: FunctionCodeBody[T], datatype?: number) {
    logger.debug('注册请求处理:', { fc, datatype });

    let reqId: ReqId;
    try {
      reqId = this.generateReqId();
    } catch (err) {
      logger.error('生成reqId失败:', { error: err, fc, datatype });
      return Promise.reject(err);
    }

    return new Promise((resolve, reject) => {
      const cleanup = () => {
        this.unregisterHandler(reqId);
        logger.debug('清理请求处理器:', { fc, reqId });
      };

      this.registerHandler(reqId, data => {
        cleanup();
        resolve(data);
      });

      try {
        this.send(fc, reqId, body, datatype);
      } catch (err) {
        cleanup();
        logger.error('请求发送失败:', {
          error: err,
          fc,
          reqId,
          body,
          datatype,
        });
        reject(err);
      }
    });
  }
}

export const ws = WebSocketService.getInstance();
