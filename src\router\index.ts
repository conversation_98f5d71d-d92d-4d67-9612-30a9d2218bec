import { createRouter, createWebHistory } from 'vue-router';
import Misc from '../script/misc';
import { createLogger } from '../libs/logger';

const logger = createLogger('Router');
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'main',
      component: () => import('../views/MainView.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
  ],
});

router.beforeEach((to, from, next) => {
  if (import.meta.env.DEV) {
    logger.debug('路由导航:', { from: from.path, to: to.path });
  }
  // 未登录跳转到登录页
  if (!Misc.loggedIn() && to.name !== 'login') {
    logger.info('用户未登录，跳转到登录页');
    next({ name: 'login' });
  } else {
    next();
  }
});

export default router;
