/**
 * 统一日志服务模块
 * 提供分级日志功能(DEBUG/INFO/WARN/ERROR)
 */

// 日志级别枚举
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

// 日志函数类型
type LogFunction = (message: string, ...args: unknown[]) => void;

// 日志记录器接口
interface Logger {
  debug: LogFunction;
  info: LogFunction;
  warn: LogFunction;
  error: LogFunction;
}

// 日志配置接口
interface LoggerConfig {
  level: LogLevel;
  prefix?: string;
  timeZone?: number; // 时区偏移小时数
  maxLogLength?: number; // 单条日志最大长度
  enableConsole?: boolean; // 是否输出到控制台
}

// 默认配置
const defaultConfig: LoggerConfig = {
  level: LogLevel.DEBUG,
  timeZone: 8,
  maxLogLength: 10000,
  enableConsole: true,
};

// 日志级别优先级
const logLevelPriority = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3,
} as const;

class LoggerService {
  private config: LoggerConfig;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  private shouldLog = (level: LogLevel): boolean => {
    return logLevelPriority[level] >= logLevelPriority[this.config.level];
  };

  private formatTimestamp = (): string => {
    const timestamp = new Date(Date.now() + this.config.timeZone! * 60 * 60 * 1000)
      .toISOString()
      .replace('T', ' ')
      .replace('Z', '');
    return timestamp;
  };

  private truncateMessage = (message: string): string => {
    if (this.config.maxLogLength && message.length > this.config.maxLogLength) {
      return message.substring(0, this.config.maxLogLength) + '...';
    }
    return message;
  };

  private createLogFunction = (level: LogLevel): LogFunction => {
    return (message: string, ...args: unknown[]) => {
      if (!this.shouldLog(level)) return;

      const timestamp = this.formatTimestamp();
      const prefix = this.config.prefix ? `[${this.config.prefix}]` : '';
      const logMessage = this.truncateMessage(`[${timestamp}] [${level}] ${prefix} ${message}`);

      if (this.config.enableConsole) {
        switch (level) {
          case LogLevel.DEBUG:
            console.debug(logMessage, ...args);
            break;
          case LogLevel.INFO:
            console.info(logMessage, ...args);
            break;
          case LogLevel.WARN:
            console.warn(logMessage, ...args);
            break;
          case LogLevel.ERROR:
            console.error(logMessage, ...args);
            break;
        }
      }
    };
  };

  public getLogger(): Logger {
    return {
      debug: this.createLogFunction(LogLevel.DEBUG),
      info: this.createLogFunction(LogLevel.INFO),
      warn: this.createLogFunction(LogLevel.WARN),
      error: this.createLogFunction(LogLevel.ERROR),
    };
  }
}

// 不再导出默认日志实例，全部使用带前缀的日志记录器

/**
 * 创建带前缀的日志记录器
 * @param prefix 日志前缀（通常为模块名或文件名）
 * @param config 可选的日志配置
 * @returns 日志记录器实例
 *
 * @example
 * // 在模块中创建日志记录器
 * const logger = createLogger('WebSocket');
 * logger.info('连接已建立');
 *
 * // 输出: [2024-07-03 10:30:00.000] [INFO] [WebSocket] 连接已建立
 */
export const createLogger = (prefix: string, config: Partial<LoggerConfig> = {}): Logger => {
  return new LoggerService({ ...config, prefix }).getLogger();
};

/**
 * 创建带前缀的日志记录器（别名，保持向后兼容）
 * @deprecated 请使用 createLogger 替代
 */
export const createPrefixLogger = createLogger;
