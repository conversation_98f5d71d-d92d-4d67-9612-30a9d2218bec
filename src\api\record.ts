import type {
  AccountInfo,
  CancelOrderBody,
  OrderDetail,
  SellStrategy,
  TableOrderInfo,
  TablePositionInfo,
  TradeSummary,
} from '@/types';
import http from './http';

class RecordService {
  static base = '/v5/strategy';
  /**
   * 获取持仓
   */
  static getPositions() {
    return http<TablePositionInfo[]>(`${this.base}/position`);
  }
  /**
   * 获取订单
   */
  static getOrders() {
    return http<TableOrderInfo[]>(`${this.base}/order`);
  }

  /**
   * 撤销订单
   */
  static cancelOrder(data: CancelOrderBody) {
    return http<void>(`${this.base}/order/cancel`, {
      method: 'POST',
      data,
    });
  }

  /**
   * 获取订单详情
   * @param direction - 买卖方向 0 买入 1 卖出 -1 全部
   */
  static getOrderDetail(direction?: 0 | 1 | -1) {
    return http<OrderDetail[]>(`${this.base}/order/detail`, {
      params: {
        direction,
      },
    });
  }

  /**
   * 获取成交汇总
   */
  static getTrades() {
    return http<TradeSummary[]>(`${this.base}/trade`);
  }

  /**
   * 查询账户信息
   */
  static getAccountInfo() {
    return http<AccountInfo>(`${this.base}/account`);
  }

  /**
   * 启动卖出策略
   * @param id - 卖出策略id
   */
  static startSellStrategy(id: number) {
    return http<void>(`${this.base}/sell/start`, {
      method: 'POST',
      params: {
        id,
      },
    });
  }

  /**
   * 停止卖出策略
   * @param id - 卖出策略id
   */
  static stopSellStrategy(id: number) {
    return http<void>(`${this.base}/sell/stop`, {
      method: 'POST',
      params: {
        id,
      },
    });
  }

  /**
   * 保存/更新卖出策略
   * @param strategy - 卖出策略配置,如果持仓中的setting为null则不传id,否则传id
   */
  static saveSellStrategy(data: Omit<SellStrategy, 'id'> & { id?: number }) {
    return http<void>(`${this.base}/sell`, {
      method: 'POST',
      data,
    });
  }
}

export default RecordService;
