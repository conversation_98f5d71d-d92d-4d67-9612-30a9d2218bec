<script setup lang="ts">
import { RouterView } from 'vue-router';
import { defineAsyncComponent, ref } from 'vue';
import { Misc } from '@/script';
import { ElConfigProvider, type MessageConfigContext } from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

const AppHeader = defineAsyncComponent(() => import('./components/electron/AppHeader.vue'));

const message = ref<MessageConfigContext>({
  offset: Misc.isElectron() ? 50 : 16,
});
</script>

<template>
  <ElConfigProvider size="small" :message :locale="zhCn">
    <AppHeader v-if="Misc.isElectron()" />
    <RouterView flex-1 min-h-1 />
  </ElConfigProvider>
</template>

<style scoped></style>
