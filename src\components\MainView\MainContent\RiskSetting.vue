<script setup lang="ts">
import StockList from './RiskSetting/StockList.vue';
import RiskParams from './RiskSetting/RiskParams.vue';
import { RiskStockTypeEnum } from '@/enum';
</script>

<template>
  <div overflow-hidden flex p-10 gap-10>
    <StockList
      bg="[--g-panel-bg]"
      flex-1
      min-w-1
      :list-type="RiskStockTypeEnum.黑名单"
      title="黑名单设置"
    />
    <StockList
      bg="[--g-panel-bg]"
      flex-1
      min-w-1
      :list-type="RiskStockTypeEnum.重点监控"
      title="重点监控名单设置"
    />
    <RiskParams bg="[--g-panel-bg]" flex-1 min-w-1 />
  </div>
</template>

<style scoped></style>
